<template>
  <div>
    <el-dialog
      title="发布话题"
      :visible.sync="dialogVisible"
      width="50%"
      @close="handleClose"
      class="mydialog"
      :close-on-click-modal="false" 
    >
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="标题" prop="title" label-width="60px">
          <el-input v-model="form.title" placeholder="请输入话题标题" clearable maxlength="80" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="内容" label-width="60px">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
            v-model="form.content"
            maxlength="2000"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="图片" label-width="60px">
          <EUploadImgVideo
            v-model="imgList"
            :limit="5"
            :onlyImage="true"
            accept="image/png,image/jpg,image/gif,image/jpeg"
            :acceptData="imgageData"
          >
            <div>
              <p class="wen">温馨提示：</p>
              <p>（1）支持jpg/jpeg/png格式； </p>
              <p>（2）建议单张图片不超过10M，最多可上传5张；</p>
            </div>
          </EUploadImgVideo>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="tip">请注意您的发言，礼貌留言哦~</div>
        <el-button class="btns" type="primary" @click="submitForm">立即发布</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EUploadImgVideo from '@/components/EUploadImgVideo.vue'
import { questionSave,updateTopic } from '@/api/study.js';
import { getFileTypeResult } from '@/utils/tools.js'
export default {
  components:{
    EUploadImgVideo,
  },
  data() {
    return {
      dialogVisible: false,
      imgList: [],
      imgageData: {
        accept: '.png,.jpg,.gif,.jpeg',
        maxsize: 1024 * 1024 * 10,
        maxsize_text: '10M',
        imageTypeList: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
      },
      form: {
        id: '',
        title: '',
        content: '',
        courseId:this.$route.query.courseId,
        type: 2,
        userType: localStorage.getItem('userType') == '6' ? 1 : 2,
        isRecover: 2, 
        fileInfoEntityList: [],
      },
      rules: {
        title: [
          { required: true, message: '请输入主题标题', trigger: 'blur' }
        ],

      }
    }
  },
  methods: {
    handleClose() {
      this.$refs.form.resetFields()
      this.form.content = '' 
      this.form.title = ''
      this.imgList = []
    },
    openDialog(detailData) {
      if (detailData) {
        this.form = {
          ...detailData,
          isRecover: 2,
        };
        this.imgList = detailData.fileInfoEntityList ? detailData.fileInfoEntityList.map(item => ({
          name: item.fileName,
          url: item.url,
          size: item.fileSize
        })) : [];
      } else {
        this.form.id = '';
        this.form.title = '';
        this.form.content = '';
        this.imgList = [];
      }
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.fileInfoEntityList = this.imgList.map(item => ({
            url: item.url,
            fileName: item.name,
            fileSize: item.size,
            fileType: getFileTypeResult(item.url)
          }));
          const apiCall = this.form.id ? updateTopic : questionSave;
          apiCall(this.form).then(response => {
            this.$message.success(this.form.id ? '更新成功' : '发布成功');
            this.dialogVisible = false;
            this.$emit('refresh'); 
            this.handleClose()
          })
        } else {
          return false;
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
    .dialog-footer{
        display: flex;
        justify-content: flex-end;
    }
    .tip{
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #8A8C89;
        margin-top: 10px;
        margin-right: 32px;
    }
</style>

