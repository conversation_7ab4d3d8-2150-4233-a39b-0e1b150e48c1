 <template>
   <!-- 支持同时上传图片和视频，或者只上传图片、只上传视频，可以预览、删除 -->
   <div class="any-upload-img-video">
    <!-- :data="acceptData" -->
     <el-upload
       :action="uploadApi"
       list-type="picture-card"
       :limit="limit"
       :disabled="disabled"
       :multiple="multiple"
       :accept="accept"
       :file-list="fileList"
       :on-exceed="handleExceed"
       :on-change="(file, fileList) => handleChange(file, fileList)"
       :on-success="(response, file, fileList) => handleSuccess(response, file, fileList)"
       :before-upload="file => beforeUpload(file)"
     >
       <i slot="default" class="el-icon-plus iconc scolor"></i>
       <p class="scolor">上传图片</p>
       <div slot="file" slot-scope="{ file }" class="custom-file-list">
         <img class="el-icon-close" @click="handleRemove(file)" src="../assets/images/fdelete.png" alt="">
         <img
           class="el-upload-list__item-thumbnail"
           :src="file.url"
           alt=""
         />
         <el-progress
           type="circle"
           v-if="showProgress && file.url == uploadUrl"
           :percentage="Number(file.percentage)"
           :width="64">
         </el-progress>
       </div>
     </el-upload>
     <el-row class="limit-tip">
         <slot></slot>
     </el-row>
   </div>
 </template>
 
 <script>
 const uidGenerator = () => {
   return '-' + parseInt(Math.random() * 10000 + 1, 10)
 }

 
 export default {
   name: 'EUploadImgVideo',
   props: {
     // 是否支持多选
     multiple: {
       type: Boolean,
       default: () => false
     },
     // 绑定的数据
     value: {
       type: [Array],
       required: false
     },
     // 禁用上传
     disabled: {
       type: Boolean,
       default: false,
       required: false
     },
     // 文件类型
     accept: {
       type: String,
       default: 'video/mp4,video/ogg,video/webm,image/png,image/jpg,image/gif,image/jpeg'
     },
     // 上传时附带的额外参数
     acceptData: {
       type: Object,
       default: () => {
         return {
           accept: '.mp4,.ogg,.webm,.png,.jpg,.gif,.jpeg', // 同时支持视频和图片时
           maxsize: 1024 * 1024 * 10,
           maxsize_text: '10M',
           videoTypeList: ['video/mp4', 'video/ogg', 'video/webm'],
           imageTypeList: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif']
         }
       }
     },
     // 最大数量
     limit: {
       type: Number,
       default: 10
     },
     // 仅上传图片
     onlyImage: {
       type: Boolean,
       default: () => false
     },
     // 仅上传视频
     onlyVideo: {
       type: Boolean,
       default: () => false
     }
   },
 
   watch: {
     value: {
       handler(val, oldValue) {
         if (!this.multiple) {
           this.initFileList(val)
         }
       },
       immediate: true
     }
   },
 
   data() {
     return {
       fileList: [], // 上传的文件数据
       showProgress: false,
       uploadPercentage: 0,
       uploadApi:process.env.VUE_APP_BASE_URL+'/aliyun/oss/uploadFiles',
       uploadUrl:""
     }
   },
 
   methods: {
     // 图片/视频回显
     initFileList(array) {
       if (!array || array.length === 0) {
         this.fileList = []
         return
       }
       const fileList = []
       const arr = array || []
       for (let i = 0; i < arr.length; i++) {
         fileList.push({
          ...arr[i],
           uid:arr[i].uid?arr[i].uid: uidGenerator(),
         })
       }
       this.fileList = fileList
     },
     // 其他附件上传大小限制处理
     beforeUpload(file) {
       const { videoTypeList, imageTypeList } = this.acceptData
       let isVideo = true
       if (videoTypeList) {
         isVideo = videoTypeList.indexOf(file.type) !== -1
       }
 
       let isImg = true
       if (imageTypeList) {
         isImg = imageTypeList.indexOf(file.type) !== -1
       }
 
       // 只支持视频
       if (this.onlyVideo) {
         if (!isVideo) {
          this.$message.error(`只能上传${this.acceptData.accept}的文件!`)
           return false
         }
       }
 
       // 只支持图片
       if (this.onlyImage) {
         if (!isImg) {
          this.$message.error(`只能上传${this.acceptData.accept}格式的文件!`)
           return false
         }
       }
 
       // 默认：视频和图片都支持
       if (!isVideo && !isImg) {
        this.$message.error(`只能上传${this.acceptData.accept}格式的文件!`)
         return false
       }
 
       // 文件大小限制
       const isLessThanMaxSize = file.size < this.acceptData.maxsize
       if (!isLessThanMaxSize) {
        this.$message.error(`上传文件大小不能超过${this.acceptData.maxsize_text}!`)
         return false
       } 
       return true
     },
     // 文件个数限制
     handleExceed() {
      this.$message.error(`当前限制最多上传 ${this.limit} 个文件!`)
     },
     handleChange(file, fileList, type) {
       this.uploadPercentage = 0
       if (file.status === 'ready') {
         this.showProgress = true
         this.uploadUrl = file.url
         const interval = setInterval(() => {
           if (this.uploadPercentage >= 95) {
             clearInterval(interval)
           }
           this.uploadPercentage += 1
         }, 20)
       }
       if (file.status === 'success') {
         this.uploadUrl = file.url
         this.uploadPercentage = 100
         this.showProgress = false
       }
     },
     // 上传图片成功
     handleSuccess(response, file, fileList) {
       this.showProgress = false
       if (response.status === 0) {
          this.$message({
            message: '上传成功',
            type: 'success'
          });
         const attachment = fileList.map(item => {
           return {
             name: item.response?item.response.data.fileName:item.name,
             url: item.response?item.response.data.url:item.url,
             uid: item.uid,
             type: item.raw ? item.raw.type : item.type,
             size:item.response?item.response.data.size:item.size
           }
         })
         this.fileList = attachment
         this.$emit("input", this.fileList);
       } else {
         this.$message(response.msg)
       }
     },
     // 删除
     handleRemove(file) {
       let index
       this.fileList.find((item, idx) => {
         if (item.uid === file.uid) {
           index = idx
           return
         }
       })
       if (typeof index !== 'undefined') {
         this.fileList.splice(index, 1)
       }
       this.$emit("input", this.fileList);
      //  this.$emit('change', this.fileList)
     },
   }
 }
 </script>
 
 <style lang="less" scoped>
  .scolor{
    color: #386CFC;
  }
 /deep/ .el-upload-list--picture-card .el-upload-list__item-thumbnail {
   width: 100%;
   height: 100%;
 }
 .limit-tip {
   color: #999999;
   display: inline-block;
   line-height: 20px;
   margin-top: 5px;
   font-size: 14px;
 }
 .iconc{
  margin-top: 41px;
 }

 
 .custom-file-list {
   position: relative;
   font-size: 16px;
   height: 100%;
 }
 .custom-file-list .el-icon-close {
     position: absolute;
     right: 0;
     top: 0;
     z-index: 1000;
     display: block !important;
   }
 
 /deep/ .el-upload-list--picture-card .el-upload-list__item {
  width: 180px;
  height: 135px;
  border-radius: 8px 8px 8px 8px;
  border: none !important;
 }
 /deep/ .el-upload--picture-card {
    width: 180px;
    height: 135px;
    background: #F0F1F5;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #F5F6FA;
    line-height: 20px;
 }
 /deep/ .el-upload-list--picture-card .el-progress {
   width: 75px !important;
 }
 /deep/ .el-progress-circle {
   width: 75px !important;
   height: 75px !important;
 }

 </style>