<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <div class="box">
                <div class="leftBox">
                    <div class="boxItem" v-for="(item,index) in boxList" :key="index" @click="goDetail(item)">
                        <div class="topBox">
                            <div class="userAndtime">
                                <div class="userName">{{ item.userName }}</div>
                                <div class="time">{{ item.time }}</div>
                            </div>
                            <div class="rightBtn">
                                <div class="btn">
                                    <img src="@/assets/dianzan_def.png" alt="" class="btnImg">
                                    <div class="number">{{ item.dznm }}</div>
                                </div>
                                <div class="btn">
                                    <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                                    <div class="number">{{ item.plnm }}</div>
                                </div>
                                <div class="btn">
                                    <img src="@/assets/delete.png" alt="" class="btnImg">
                                </div>
                            </div>
                        </div>
                        <div class="content">{{ item.content }}</div>
                        <div class="imgBox">
                            <div class="imgList" v-for="(imgItem,imgIndex) in item.imgList" :key="imgIndex">
                                <img :src="imgItem.img" alt="" class="imgSize">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rightBox">
                    <div class="btnItem" @click="openTopicDialog">
                        <img src="@/assets/add_def.png" alt="" class="iconImg add-icon">
                        <span class="btnText">发布话题</span>
                    </div>
                    <div class="btnItem">
                        <img src="@/assets/send_def.png" alt="" class="iconImg send-icon">
                        <span class="btnText">我发布的</span>
                    </div>
                    <div class="btnItem">
                        <img src="@/assets/ask_def.png" alt="" class="iconImg ask-icon">
                        <span class="btnText">我回复的</span>
                    </div>
                    <div class="btnItem">
                        <img src="@/assets/ques_def.png" alt="" class="iconImg ques-icon">
                        <span class="btnText">回复我的</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布话题对话框 -->
        <DialogTopic ref="dialogTopicRef" @refresh="refreshList" />
    </div>
</template>
    
<script setup>
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import tu1 from '@/assets/1.png'
import DialogTopic from './dialogTopic.vue'

const router = useRouter()
const dialogTopicRef = ref()

const goDetail = (item) => {
    router.push({
        path: '/jiaoyanshi/discussDetail',
        query: {
            id: item.id
        }
    })
}

// 打开发布话题对话框
const openTopicDialog = () => {
    dialogTopicRef.value.openDialog()
}

// 刷新列表
const refreshList = () => {
    // 这里可以添加刷新列表的逻辑
    console.log('刷新话题列表')
}

const boxList = ref([
    {
        id: 1,
        userName:'周雨楠',
        time:'2024-09-06  10:24:26',
        dznm: 123,
        plnm: 123,
        content:'1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会办法安排老师去办法安排老师去',
        imgList: [
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
        ]
    },
    {
        id: 2,
        userName:'周雨楠',
        time:'2024-09-06  10:24:26',
        dznm: 123,
        plnm: 123,
        content:'1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会办法安排老师去办法安排老师去',
        imgList: [
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
            { img: tu1 },
        ]
    },
])
</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.box{
    display: flex;
    margin-top: 20px;
}
.leftBox{
    margin-right: 20px;
}
.rightBox{
    width: 236px;
    height: 300px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 30px;
}
.boxItem{
    width: 1024px;
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.btnItem{
    width: 176px;
    height: 48px;
    border-radius: 8px 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 16px;
}
.btnItem:hover{
    background: #386CFC;
    transition: all 0.3s ease;
    .btnText{
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        transition: all 0.3s ease;
    }
    .add-icon{
        content: url('@/assets/add.png');
    }
    .send-icon{
        content: url('@/assets/send.png');
    }
    .ask-icon{
        content: url('@/assets/ask.png');
    }
    .ques-icon{
        content: url('@/assets/ques.png');
    }
}
.iconImg{
    width: 20px;
    height: 20px;
    margin-right: 8px;
}
.btnText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #44474D;
    transition: all 0.3s ease;
}
</style>
  